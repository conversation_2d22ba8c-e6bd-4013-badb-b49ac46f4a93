import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import {
  CityProductFare,
  CityProductFareStatus,
} from './models/cityProductFare.model';

@Injectable()
export class CityProductFareRepository extends BaseRepository<CityProductFare> {
  protected readonly modelName = 'cityProductFare';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new city product fare record.
   * @param data - City product fare data excluding id and timestamps
   */
  async createCityProductFare(
    data: Omit<CityProductFare, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<CityProductFare> {
    return this.create(data);
  }

  /**
   * Find all city product fares for a specific city product.
   * @param cityProductId - City product ID
   */
  async findCityProductFaresByCityProductId(
    cityProductId: string,
  ): Promise<CityProductFare[]> {
    return this.findMany({
      where: { cityProductId },
      include: {
        fromZone: true,
        toZone: true,
        fromZoneType: true,
        toZoneType: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Find city product fare by ID with relations.
   * @param id - City product fare ID
   */
  async findCityProductFareById(id: string): Promise<CityProductFare | null> {
    return this.findById(id, {
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
    });
  }

  /**
   * Update city product fare by ID.
   * @param id - City product fare ID
   * @param data - Partial city product fare data
   */
  async updateCityProductFare(
    id: string,
    data: Partial<CityProductFare>,
  ): Promise<CityProductFare> {
    return this.updateById(id, data);
  }

  /**
   * Soft delete city product fare by ID.
   * @param id - City product fare ID
   */
  async deleteCityProductFare(id: string): Promise<CityProductFare> {
    return this.softDeleteById(id);
  }

  /**
   * Get paginated city product fares.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional query options
   */
  async paginateCityProductFares(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, {
      ...options,
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Find fare rules that match pickup and destination zones.
   * @param pickupZoneId - Pickup zone ID (can be null for default rules)
   * @param destinationZoneId - Destination zone ID (can be null for default rules)
   */
  async findMatchingFareRules(
    pickupZoneId?: string | null,
    destinationZoneId?: string | null,
  ): Promise<CityProductFare[]> {
    return this.findMany({
      where: {
        status: CityProductFareStatus.ACTIVE,
        OR: [
          {
            fromZoneId: pickupZoneId,
            toZoneId: destinationZoneId,
          },
          {
            fromZoneId: pickupZoneId,
            toZoneId: null,
          },
          {
            fromZoneId: null,
            toZoneId: destinationZoneId,
          },
          {
            fromZoneId: null,
            toZoneId: null,
          },
        ],
      },
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Attach charge groups to a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupIds - Array of charge group IDs with priorities
   */
  async attachChargeGroupsToFare(
    cityProductFareId: string,
    chargeGroupIds: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    return this.prisma.$transaction(async (tx) => {
      for (const { chargeGroupId, priority } of chargeGroupIds) {
        // Try to update priority if exists, else create new association
        const updated = await tx.fareChargeGroup.updateMany({
          where: {
            cityProductFareId,
            chargeGroupId,
          },
          data: { priority },
        });
        if (updated.count === 0) {
          await tx.fareChargeGroup.create({
            data: {
              cityProductFareId,
              chargeGroupId,
              priority,
            },
          });
        }
      }

      return tx.fareChargeGroup.findMany({
        where: { cityProductFareId },
        include: {
          chargeGroup: true,
        },
        orderBy: { priority: 'asc' },
      });
    });
  }

  /**
   * Detach a charge group from a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupId - Charge group ID
   */
  async detachChargeGroupFromFare(
    cityProductFareId: string,
    chargeGroupId: string,
  ): Promise<void> {
    await this.prisma.fareChargeGroup.deleteMany({
      where: {
        cityProductFareId,
        chargeGroupId,
      },
    });
  }

  /**
   * Update charge group priorities for a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupPriorities - Array of charge group IDs with new priorities
   */
  async updateChargeGroupPriorities(
    cityProductFareId: string,
    chargeGroupPriorities: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    // Update priorities for existing charge groups using Prisma repo functions
    for (const { chargeGroupId, priority } of chargeGroupPriorities) {
      await this.prisma.fareChargeGroup.updateMany({
        where: {
          cityProductFareId,
          chargeGroupId,
        },
        data: {
          priority,
          updatedAt: new Date(),
        },
      });
    }

    // Return updated fare charge groups using Prisma
    return this.prisma.fareChargeGroup.findMany({
      where: { cityProductFareId },
      include: {
        chargeGroup: true,
      },
      orderBy: { priority: 'asc' },
    });
  }

  /**
   * Get charge groups for a specific fare rule.
   * @param cityProductFareId - City product fare ID
   */
  async getFareChargeGroups(cityProductFareId: string): Promise<any[]> {
    return this.prisma.fareChargeGroup.findMany({
      where: { cityProductFareId },
      include: {
        chargeGroup: true,
      },
      orderBy: { priority: 'asc' },
    });
  }

  /**
   * Check if a fare rule exists for a city product with specific zones.
   * @param cityProductId - City product ID
   * @param fromZoneId - From zone ID (nullable)
   * @param toZoneId - To zone ID (nullable)
   */
  async fareRuleExists(
    cityProductId: string,
    fromZoneId?: string | null,
    toZoneId?: string | null,
    fromZoneTypeId?: string | null,
    toZoneTypeId?: string | null,
  ): Promise<any> {
    return this.findOne({
      where: {
        cityProductId,
        fromZoneId,
        toZoneId,
        fromZoneTypeId,
        toZoneTypeId,
      },
    });
  }

  /**
   * Find the maximum priority (most efficient) fare for each city product based on the priority ranking system.
   * Implements a 9-tier priority ranking system for fare matching using a single optimized database query.
   *
   * Priority Ranking (lower rank number = higher priority):
   * 1. Exact zone match: fromZoneId = pickupZone AND toZoneId = destinationZone
   * 2. Stops override (zone): any stopsZones match toZoneId
   * 3. Stops override (zone type): any stopsZoneTypes match toZoneTypeId
   * 4. Exact destination zone: toZoneId = destinationZone
   * 5. Exact pickup zone: fromZoneId = pickupZone
   * 6. ZoneType → ZoneType match: fromZoneTypeId = pickupZoneType AND toZoneTypeId = destinationZoneType
   * 7. Destination type-only: toZoneTypeId = destinationZoneType
   * 8. Pickup type-only: fromZoneTypeId = pickupZoneType
   * 9. Fallback / default: fromZoneId = null AND toZoneId = null
   *
   * @param cityProductIds - Array of city product IDs to find fares for
   * @param pickupZoneId - Pickup zone ID (nullable)
   * @param destinationZoneId - Destination zone ID (nullable)
   * @param stopsZoneIds - Array of stop zone IDs (nullable)
   */
  async findMaxPriorityFareForCityProducts(
    cityProductIds: string[],
    pickupZoneId?: string | null,
    destinationZoneId?: string | null,
    stopsZoneIds?: string[] | null,
  ): Promise<CityProductFare[]> {
    if (cityProductIds.length === 0) {
      return [];
    }

    // Get zone type information for pickup and destination zones in parallel
    const [pickupZone, destinationZone, stopsZones] = await Promise.all([
      pickupZoneId ? this.prisma.zone.findUnique({
        where: { id: pickupZoneId },
        include: { zoneType: true }
      }) : Promise.resolve(null),
      destinationZoneId ? this.prisma.zone.findUnique({
        where: { id: destinationZoneId },
        include: { zoneType: true }
      }) : Promise.resolve(null),
      stopsZoneIds && stopsZoneIds.length > 0 ? this.prisma.zone.findMany({
        where: { id: { in: stopsZoneIds } },
        include: { zoneType: true }
      }) : Promise.resolve([])
    ]);

    const pickupZoneTypeId = pickupZone?.zoneTypeId || null;
    const destinationZoneTypeId = destinationZone?.zoneTypeId || null;
    const stopsZoneTypeIds = stopsZones.map(zone => zone.zoneTypeId).filter((id): id is string => Boolean(id));

    // Build comprehensive OR conditions to fetch ALL potentially matching fares in a single query
    const orConditions = [];

    // Priority Rank 1: Exact zone match
    if (pickupZoneId && destinationZoneId) {
      orConditions.push({
        fromZoneId: pickupZoneId,
        toZoneId: destinationZoneId
      });
    }

    // Priority Rank 2: Stops override (zone) - any stop zone matches toZoneId
    if (stopsZoneIds && stopsZoneIds.length > 0) {
      orConditions.push({
        toZoneId: { in: stopsZoneIds }
      });
    }

    // Priority Rank 3: Stops override (zone type) - any stop zone type matches toZoneTypeId
    if (stopsZoneTypeIds.length > 0) {
      orConditions.push({
        toZoneTypeId: { in: stopsZoneTypeIds }
      });
    }

    // Priority Rank 4: Exact destination zone
    if (destinationZoneId) {
      orConditions.push({
        toZoneId: destinationZoneId,
        fromZoneId: null
      });
    }

    // Priority Rank 5: Exact pickup zone
    if (pickupZoneId) {
      orConditions.push({
        fromZoneId: pickupZoneId,
        toZoneId: null
      });
    }

    // Priority Rank 6: ZoneType → ZoneType match
    if (pickupZoneTypeId && destinationZoneTypeId) {
      orConditions.push({
        fromZoneTypeId: pickupZoneTypeId,
        toZoneTypeId: destinationZoneTypeId,
        fromZoneId: null,
        toZoneId: null
      });
    }

    // Priority Rank 7: Destination type-only
    if (destinationZoneTypeId) {
      orConditions.push({
        toZoneTypeId: destinationZoneTypeId,
        fromZoneTypeId: null,
        fromZoneId: null,
        toZoneId: null
      });
    }

    // Priority Rank 8: Pickup type-only
    if (pickupZoneTypeId) {
      orConditions.push({
        fromZoneTypeId: pickupZoneTypeId,
        toZoneTypeId: null,
        fromZoneId: null,
        toZoneId: null
      });
    }

    // Priority Rank 9: Fallback / default (always included as last resort)
    orConditions.push({
      fromZoneId: null,
      toZoneId: null,
      fromZoneTypeId: null,
      toZoneTypeId: null
    });

    // Single optimized query to fetch ALL potentially matching fares
    const allMatchingFares = await this.findMany({
      where: {
        cityProductId: { in: cityProductIds },
        status: CityProductFareStatus.ACTIVE,
        OR: orConditions
      },
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fromZoneType: true,
        toZoneType: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc', // Order by database priority value (lowest = highest priority)
      },
    });

    // In-memory priority ranking logic to select the best fare for each city product
    const results: CityProductFare[] = [];

    for (const cityProductId of cityProductIds) {
      const cityProductFares = allMatchingFares.filter(fare => fare.cityProductId === cityProductId);

      if (cityProductFares.length === 0) {
        continue; // No fares found for this city product
      }

      let bestFare: CityProductFare | null = null;

      // Apply priority ranking logic - check each ranking level in order
      for (let rankLevel = 1; rankLevel <= 9; rankLevel++) {
        const faresAtThisRank = cityProductFares.filter(fare =>
          this.fareMatchesRankingLevel(fare, rankLevel, {
            pickupZoneId,
            destinationZoneId,
            stopsZoneIds,
            pickupZoneTypeId,
            destinationZoneTypeId,
            stopsZoneTypeIds
          })
        );

        if (faresAtThisRank.length > 0) {
          // Found fares at this ranking level - select the one with lowest priority value
          bestFare = faresAtThisRank.reduce((best, current) =>
            current.priority < best.priority ? current : best
          );
          break; // Stop at the first (highest priority) ranking level that has matches
        }
      }

      if (bestFare) {
        results.push(bestFare);
      }
    }

    return results;
  }

  /**
   * Helper method to determine if a fare matches a specific priority ranking level
   * @param fare - The fare to check
   * @param rankLevel - The priority ranking level (1-9)
   * @param context - Context containing zone and zone type information
   */
  private fareMatchesRankingLevel(
    fare: CityProductFare,
    rankLevel: number,
    context: {
      pickupZoneId?: string | null | undefined;
      destinationZoneId?: string | null | undefined;
      stopsZoneIds?: string[] | null | undefined;
      pickupZoneTypeId?: string | null | undefined;
      destinationZoneTypeId?: string | null | undefined;
      stopsZoneTypeIds?: string[] | undefined;
    }
  ): boolean {
    const {
      pickupZoneId,
      destinationZoneId,
      stopsZoneIds,
      pickupZoneTypeId,
      destinationZoneTypeId,
      stopsZoneTypeIds
    } = context;

    switch (rankLevel) {
      case 1: // Exact zone match
        return !!(pickupZoneId && destinationZoneId &&
          fare.fromZoneId === pickupZoneId &&
          fare.toZoneId === destinationZoneId);

      case 2: // Stops override (zone)
        return !!(stopsZoneIds && stopsZoneIds.length > 0 &&
          fare.toZoneId && stopsZoneIds.includes(fare.toZoneId));

      case 3: // Stops override (zone type)
        return !!(stopsZoneTypeIds && stopsZoneTypeIds.length > 0 &&
          fare.toZoneTypeId &&
          stopsZoneTypeIds.includes(fare.toZoneTypeId));

      case 4: // Exact destination zone
        return !!(destinationZoneId &&
          fare.toZoneId === destinationZoneId &&
          fare.fromZoneId === null);

      case 5: // Exact pickup zone
        return !!(pickupZoneId &&
          fare.fromZoneId === pickupZoneId &&
          fare.toZoneId === null);

      case 6: // ZoneType → ZoneType match
        return !!(pickupZoneTypeId && destinationZoneTypeId &&
          fare.fromZoneTypeId === pickupZoneTypeId &&
          fare.toZoneTypeId === destinationZoneTypeId &&
          fare.fromZoneId === null &&
          fare.toZoneId === null);

      case 7: // Destination type-only
        return !!(destinationZoneTypeId &&
          fare.toZoneTypeId === destinationZoneTypeId &&
          fare.fromZoneTypeId === null &&
          fare.fromZoneId === null &&
          fare.toZoneId === null);

      case 8: // Pickup type-only
        return !!(pickupZoneTypeId &&
          fare.fromZoneTypeId === pickupZoneTypeId &&
          fare.toZoneTypeId === null &&
          fare.fromZoneId === null &&
          fare.toZoneId === null);

      case 9: // Fallback / default
        return fare.fromZoneId === null &&
          fare.toZoneId === null &&
          fare.fromZoneTypeId === null &&
          fare.toZoneTypeId === null;

      default:
        return false;
    }
  }
}
