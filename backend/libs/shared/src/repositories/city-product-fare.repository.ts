import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import {
  CityProductFare,
  CityProductFareStatus,
} from './models/cityProductFare.model';

@Injectable()
export class CityProductFareRepository extends BaseRepository<CityProductFare> {
  protected readonly modelName = 'cityProductFare';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new city product fare record.
   * @param data - City product fare data excluding id and timestamps
   */
  async createCityProductFare(
    data: Omit<CityProductFare, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<CityProductFare> {
    return this.create(data);
  }

  /**
   * Find all city product fares for a specific city product.
   * @param cityProductId - City product ID
   */
  async findCityProductFaresByCityProductId(
    cityProductId: string,
  ): Promise<CityProductFare[]> {
    return this.findMany({
      where: { cityProductId },
      include: {
        fromZone: true,
        toZone: true,
        fromZoneType: true,
        toZoneType: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Find city product fare by ID with relations.
   * @param id - City product fare ID
   */
  async findCityProductFareById(id: string): Promise<CityProductFare | null> {
    return this.findById(id, {
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
    });
  }

  /**
   * Update city product fare by ID.
   * @param id - City product fare ID
   * @param data - Partial city product fare data
   */
  async updateCityProductFare(
    id: string,
    data: Partial<CityProductFare>,
  ): Promise<CityProductFare> {
    return this.updateById(id, data);
  }

  /**
   * Soft delete city product fare by ID.
   * @param id - City product fare ID
   */
  async deleteCityProductFare(id: string): Promise<CityProductFare> {
    return this.softDeleteById(id);
  }

  /**
   * Get paginated city product fares.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional query options
   */
  async paginateCityProductFares(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, {
      ...options,
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Find fare rules that match pickup and destination zones.
   * @param pickupZoneId - Pickup zone ID (can be null for default rules)
   * @param destinationZoneId - Destination zone ID (can be null for default rules)
   */
  async findMatchingFareRules(
    pickupZoneId?: string | null,
    destinationZoneId?: string | null,
  ): Promise<CityProductFare[]> {
    return this.findMany({
      where: {
        status: CityProductFareStatus.ACTIVE,
        OR: [
          {
            fromZoneId: pickupZoneId,
            toZoneId: destinationZoneId,
          },
          {
            fromZoneId: pickupZoneId,
            toZoneId: null,
          },
          {
            fromZoneId: null,
            toZoneId: destinationZoneId,
          },
          {
            fromZoneId: null,
            toZoneId: null,
          },
        ],
      },
      include: {
        cityProduct: true,
        fromZone: true,
        toZone: true,
        fareChargeGroups: {
          include: {
            chargeGroup: true,
          },
          orderBy: {
            priority: 'asc',
          },
        },
      },
      orderBy: {
        priority: 'asc',
      },
    });
  }

  /**
   * Attach charge groups to a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupIds - Array of charge group IDs with priorities
   */
  async attachChargeGroupsToFare(
    cityProductFareId: string,
    chargeGroupIds: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    return this.prisma.$transaction(async (tx) => {
      for (const { chargeGroupId, priority } of chargeGroupIds) {
        // Try to update priority if exists, else create new association
        const updated = await tx.fareChargeGroup.updateMany({
          where: {
            cityProductFareId,
            chargeGroupId,
          },
          data: { priority },
        });
        if (updated.count === 0) {
          await tx.fareChargeGroup.create({
            data: {
              cityProductFareId,
              chargeGroupId,
              priority,
            },
          });
        }
      }

      return tx.fareChargeGroup.findMany({
        where: { cityProductFareId },
        include: {
          chargeGroup: true,
        },
        orderBy: { priority: 'asc' },
      });
    });
  }

  /**
   * Detach a charge group from a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupId - Charge group ID
   */
  async detachChargeGroupFromFare(
    cityProductFareId: string,
    chargeGroupId: string,
  ): Promise<void> {
    await this.prisma.fareChargeGroup.deleteMany({
      where: {
        cityProductFareId,
        chargeGroupId,
      },
    });
  }

  /**
   * Update charge group priorities for a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupPriorities - Array of charge group IDs with new priorities
   */
  async updateChargeGroupPriorities(
    cityProductFareId: string,
    chargeGroupPriorities: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    // Update priorities for existing charge groups using Prisma repo functions
    for (const { chargeGroupId, priority } of chargeGroupPriorities) {
      await this.prisma.fareChargeGroup.updateMany({
        where: {
          cityProductFareId,
          chargeGroupId,
        },
        data: {
          priority,
          updatedAt: new Date(),
        },
      });
    }

    // Return updated fare charge groups using Prisma
    return this.prisma.fareChargeGroup.findMany({
      where: { cityProductFareId },
      include: {
        chargeGroup: true,
      },
      orderBy: { priority: 'asc' },
    });
  }

  /**
   * Get charge groups for a specific fare rule.
   * @param cityProductFareId - City product fare ID
   */
  async getFareChargeGroups(cityProductFareId: string): Promise<any[]> {
    return this.prisma.fareChargeGroup.findMany({
      where: { cityProductFareId },
      include: {
        chargeGroup: true,
      },
      orderBy: { priority: 'asc' },
    });
  }

  /**
   * Check if a fare rule exists for a city product with specific zones.
   * @param cityProductId - City product ID
   * @param fromZoneId - From zone ID (nullable)
   * @param toZoneId - To zone ID (nullable)
   */
  async fareRuleExists(
    cityProductId: string,
    fromZoneId?: string | null,
    toZoneId?: string | null,
    fromZoneTypeId?: string | null,
    toZoneTypeId?: string | null,
  ): Promise<any> {
    return this.findOne({
      where: {
        cityProductId,
        fromZoneId,
        toZoneId,
        fromZoneTypeId,
        toZoneTypeId,
      },
    });
  }

  /**
   * Find the maximum priority (most efficient) fare for each city product based on the priority ranking system.
   * Implements a 9-tier priority ranking system for fare matching.
   *
   * Priority Ranking (lower number = higher priority):
   * 1. Exact zone match: fromZoneId = pickupZone AND toZoneId = destinationZone
   * 2. Stops override (zone): any stopsZones match toZoneId
   * 3. Stops override (zone type): any stopsZoneTypes match toZoneTypeId
   * 4. Exact destination zone: toZoneId = destinationZone
   * 5. Exact pickup zone: fromZoneId = pickupZone
   * 6. ZoneType → ZoneType match: fromZoneTypeId = pickupZoneType AND toZoneTypeId = destinationZoneType
   * 7. Destination type-only: toZoneTypeId = destinationZoneType
   * 8. Pickup type-only: fromZoneTypeId = pickupZoneType
   * 9. Fallback / default: fromZoneId = null AND toZoneId = null
   *
   * @param cityProductIds - Array of city product IDs to find fares for
   * @param pickupZoneId - Pickup zone ID (nullable)
   * @param destinationZoneId - Destination zone ID (nullable)
   * @param stopsZoneIds - Array of stop zone IDs (nullable)
   */
  async findMaxPriorityFareForCityProducts(
    cityProductIds: string[],
    pickupZoneId?: string | null,
    destinationZoneId?: string | null,
    stopsZoneIds?: string[] | null,
  ): Promise<CityProductFare[]> {
    if (cityProductIds.length === 0) {
      return [];
    }

    // First, get zone type information for pickup and destination zones
    const pickupZone = pickupZoneId ? await this.prisma.zone.findUnique({
      where: { id: pickupZoneId },
      include: { zoneType: true }
    }) : null;

    const destinationZone = destinationZoneId ? await this.prisma.zone.findUnique({
      where: { id: destinationZoneId },
      include: { zoneType: true }
    }) : null;

    // Get zone type information for stops
    const stopsZones = stopsZoneIds && stopsZoneIds.length > 0 ? await this.prisma.zone.findMany({
      where: { id: { in: stopsZoneIds } },
      include: { zoneType: true }
    }) : [];

    const pickupZoneTypeId = pickupZone?.zoneTypeId || null;
    const destinationZoneTypeId = destinationZone?.zoneTypeId || null;
    const stopsZoneTypeIds = stopsZones.map(zone => zone.zoneTypeId).filter(Boolean);

    // Build the query conditions for each priority level
    const whereConditions = [];

    // Priority 1: Exact zone match
    if (pickupZoneId && destinationZoneId) {
      whereConditions.push({
        fromZoneId: pickupZoneId,
        toZoneId: destinationZoneId,
        priority: 1
      });
    }

    // Priority 2: Stops override (zone) - any stop zone matches toZoneId
    if (stopsZoneIds && stopsZoneIds.length > 0) {
      whereConditions.push({
        toZoneId: { in: stopsZoneIds },
        priority: 2
      });
    }

    // Priority 3: Stops override (zone type) - any stop zone type matches toZoneTypeId
    if (stopsZoneTypeIds.length > 0) {
      whereConditions.push({
        toZoneTypeId: { in: stopsZoneTypeIds },
        priority: 3
      });
    }

    // Priority 4: Exact destination zone
    if (destinationZoneId) {
      whereConditions.push({
        toZoneId: destinationZoneId,
        fromZoneId: null,
        priority: 4
      });
    }

    // Priority 5: Exact pickup zone
    if (pickupZoneId) {
      whereConditions.push({
        fromZoneId: pickupZoneId,
        toZoneId: null,
        priority: 5
      });
    }

    // Priority 6: ZoneType → ZoneType match
    if (pickupZoneTypeId && destinationZoneTypeId) {
      whereConditions.push({
        fromZoneTypeId: pickupZoneTypeId,
        toZoneTypeId: destinationZoneTypeId,
        fromZoneId: null,
        toZoneId: null,
        priority: 6
      });
    }

    // Priority 7: Destination type-only
    if (destinationZoneTypeId) {
      whereConditions.push({
        toZoneTypeId: destinationZoneTypeId,
        fromZoneTypeId: null,
        fromZoneId: null,
        toZoneId: null,
        priority: 7
      });
    }

    // Priority 8: Pickup type-only
    if (pickupZoneTypeId) {
      whereConditions.push({
        fromZoneTypeId: pickupZoneTypeId,
        toZoneTypeId: null,
        fromZoneId: null,
        toZoneId: null,
        priority: 8
      });
    }

    // Priority 9: Fallback / default
    whereConditions.push({
      fromZoneId: null,
      toZoneId: null,
      fromZoneTypeId: null,
      toZoneTypeId: null,
      priority: 9
    });

    // Execute the query to find the best fare for each city product
    const results: CityProductFare[] = [];

    for (const cityProductId of cityProductIds) {
      let bestFare: CityProductFare | null = null;

      // Try each priority level in order until we find a match
      for (const condition of whereConditions) {
        const fare = await this.findOne({
          where: {
            cityProductId,
            status: CityProductFareStatus.ACTIVE,
            ...condition
          },
          include: {
            cityProduct: true,
            fromZone: true,
            toZone: true,
            fromZoneType: true,
            toZoneType: true,
            fareChargeGroups: {
              include: {
                chargeGroup: true,
              },
              orderBy: {
                priority: 'asc',
              },
            },
          },
        });

        if (fare) {
          bestFare = fare;
          break; // Found a match at this priority level, stop searching
        }
      }

      if (bestFare) {
        results.push(bestFare);
      }
    }

    return results;
  }
}
