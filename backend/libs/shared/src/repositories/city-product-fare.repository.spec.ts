import { Test, TestingModule } from '@nestjs/testing';
import { CityProductFareRepository } from './city-product-fare.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { CityProductFareStatus } from './models/cityProductFare.model';

describe('CityProductFareRepository - findMaxPriorityFareForCityProducts (Optimized)', () => {
  let repository: CityProductFareRepository;
  let mockPrisma: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    const mockPrismaService = {
      zone: {
        findUnique: jest.fn(),
        findMany: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CityProductFareRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<CityProductFareRepository>(CityProductFareRepository);
    mockPrisma = module.get(PrismaService);
  });

  describe('Single Query Optimization Tests', () => {
    const cityProductIds = ['cp1', 'cp2'];
    const pickupZoneId = 'pickup-zone-1';
    const destinationZoneId = 'dest-zone-1';
    const stopsZoneIds = ['stop-zone-1', 'stop-zone-2'];

    const mockPickupZone = {
      id: pickupZoneId,
      name: 'Pickup Zone',
      zoneTypeId: 'pickup-zone-type-1',
      zoneType: { id: 'pickup-zone-type-1', name: 'Airport' }
    };

    const mockDestinationZone = {
      id: destinationZoneId,
      name: 'Destination Zone',
      zoneTypeId: 'dest-zone-type-1',
      zoneType: { id: 'dest-zone-type-1', name: 'City Center' }
    };

    const mockStopsZones = [
      {
        id: 'stop-zone-1',
        name: 'Stop Zone 1',
        zoneTypeId: 'stop-zone-type-1',
        zoneType: { id: 'stop-zone-type-1', name: 'Mall' }
      },
      {
        id: 'stop-zone-2',
        name: 'Stop Zone 2',
        zoneTypeId: 'stop-zone-type-2',
        zoneType: { id: 'stop-zone-type-2', name: 'Hotel' }
      }
    ];

    beforeEach(() => {
      // Setup zone mocks
      mockPrisma.zone.findUnique
        .mockImplementation((args) => {
          if (args.where.id === pickupZoneId) {
            return Promise.resolve(mockPickupZone);
          }
          if (args.where.id === destinationZoneId) {
            return Promise.resolve(mockDestinationZone);
          }
          return Promise.resolve(null);
        });

      mockPrisma.zone.findMany.mockResolvedValue(mockStopsZones);
    });

    it('should return empty array when no city products provided', async () => {
      const result = await repository.findMaxPriorityFareForCityProducts(
        [],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toEqual([]);
    });

    it('should use single query to fetch all matching fares and apply priority ranking in-memory', async () => {
      const mockFares = [
        {
          id: 'fare1',
          cityProductId: 'cp1',
          priority: 5, // Database priority value
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: pickupZoneId,
          toZoneId: destinationZoneId,
          fromZoneTypeId: null,
          toZoneTypeId: null,
          fareChargeGroups: []
        },
        {
          id: 'fare2',
          cityProductId: 'cp1',
          priority: 10, // Higher database priority value (lower priority)
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: null,
          toZoneId: destinationZoneId,
          fromZoneTypeId: null,
          toZoneTypeId: null,
          fareChargeGroups: []
        },
        {
          id: 'fare3',
          cityProductId: 'cp2',
          priority: 15,
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: null,
          toZoneId: null,
          fromZoneTypeId: null,
          toZoneTypeId: null,
          fareChargeGroups: []
        }
      ];

      // Mock the single findMany call
      jest.spyOn(repository, 'findMany').mockResolvedValue(mockFares);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1', 'cp2'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      // Should call findMany only once with comprehensive OR conditions
      expect(repository.findMany).toHaveBeenCalledTimes(1);
      expect(repository.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            cityProductId: { in: ['cp1', 'cp2'] },
            status: CityProductFareStatus.ACTIVE,
            OR: expect.arrayContaining([
              // Should include all priority ranking conditions
              expect.objectContaining({
                fromZoneId: pickupZoneId,
                toZoneId: destinationZoneId
              }),
              expect.objectContaining({
                toZoneId: { in: stopsZoneIds }
              }),
              expect.objectContaining({
                fromZoneId: null,
                toZoneId: null,
                fromZoneTypeId: null,
                toZoneTypeId: null
              })
            ])
          }),
          orderBy: {
            priority: 'asc'
          }
        })
      );

      // Should return best fare for each city product based on priority ranking
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('fare1'); // cp1: exact zone match (rank 1) with priority 5
      expect(result[1].id).toBe('fare3'); // cp2: fallback (rank 9) with priority 15
    });

    it('should select fare with lowest database priority value when multiple fares match same ranking level', async () => {
      const mockFares = [
        {
          id: 'fare1',
          cityProductId: 'cp1',
          priority: 20, // Higher database priority value
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: pickupZoneId,
          toZoneId: destinationZoneId,
          fromZoneTypeId: null,
          toZoneTypeId: null,
          fareChargeGroups: []
        },
        {
          id: 'fare2',
          cityProductId: 'cp1',
          priority: 5, // Lower database priority value (should be selected)
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: pickupZoneId,
          toZoneId: destinationZoneId,
          fromZoneTypeId: null,
          toZoneTypeId: null,
          fareChargeGroups: []
        }
      ];

      jest.spyOn(repository, 'findMany').mockResolvedValue(mockFares);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('fare2'); // Should select fare with priority 5 (lower value)
      expect(result[0].priority).toBe(5);
    });

    it('should prioritize higher ranking level over lower database priority value', async () => {
      const mockFares = [
        {
          id: 'fare1',
          cityProductId: 'cp1',
          priority: 1, // Very low database priority value
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: null,
          toZoneId: null,
          fromZoneTypeId: null,
          toZoneTypeId: null, // Fallback fare (rank 9)
          fareChargeGroups: []
        },
        {
          id: 'fare2',
          cityProductId: 'cp1',
          priority: 100, // High database priority value
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: pickupZoneId,
          toZoneId: destinationZoneId, // Exact zone match (rank 1)
          fromZoneTypeId: null,
          toZoneTypeId: null,
          fareChargeGroups: []
        }
      ];

      jest.spyOn(repository, 'findMany').mockResolvedValue(mockFares);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('fare2'); // Should select exact zone match despite higher database priority
      expect(result[0].priority).toBe(100);
    });

    it('should handle null zones gracefully', async () => {
      const mockFallbackFare = {
        id: 'fare9',
        cityProductId: 'cp1',
        priority: 100,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: null,
        fromZoneTypeId: null,
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      jest.spyOn(repository, 'findMany').mockResolvedValue([mockFallbackFare]);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        null,
        null,
        null
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFallbackFare);
    });

    it('should return empty array when no fares found for any city product', async () => {
      jest.spyOn(repository, 'findMany').mockResolvedValue([]);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toEqual([]);
    });
  });
});
