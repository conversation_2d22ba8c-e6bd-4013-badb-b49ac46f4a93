import { Test, TestingModule } from '@nestjs/testing';
import { CityProductFareRepository } from './city-product-fare.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { CityProductFareStatus } from './models/cityProductFare.model';

describe('CityProductFareRepository - findMaxPriorityFareForCityProducts', () => {
  let repository: CityProductFareRepository;
  let mockPrisma: jest.Mocked<PrismaService>;

  beforeEach(async () => {
    const mockPrismaService = {
      zone: {
        findUnique: jest.fn(),
        findMany: jest.fn(),
      },
      cityProductFare: {
        findFirst: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CityProductFareRepository,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    repository = module.get<CityProductFareRepository>(CityProductFareRepository);
    mockPrisma = module.get(PrismaService);
  });

  describe('Priority Ranking Tests', () => {
    const cityProductIds = ['cp1', 'cp2'];
    const pickupZoneId = 'pickup-zone-1';
    const destinationZoneId = 'dest-zone-1';
    const stopsZoneIds = ['stop-zone-1', 'stop-zone-2'];

    const mockPickupZone = {
      id: pickupZoneId,
      name: 'Pickup Zone',
      zoneTypeId: 'pickup-zone-type-1',
      zoneType: { id: 'pickup-zone-type-1', name: 'Airport' }
    };

    const mockDestinationZone = {
      id: destinationZoneId,
      name: 'Destination Zone',
      zoneTypeId: 'dest-zone-type-1',
      zoneType: { id: 'dest-zone-type-1', name: 'City Center' }
    };

    const mockStopsZones = [
      {
        id: 'stop-zone-1',
        name: 'Stop Zone 1',
        zoneTypeId: 'stop-zone-type-1',
        zoneType: { id: 'stop-zone-type-1', name: 'Mall' }
      },
      {
        id: 'stop-zone-2',
        name: 'Stop Zone 2',
        zoneTypeId: 'stop-zone-type-2',
        zoneType: { id: 'stop-zone-type-2', name: 'Hotel' }
      }
    ];

    beforeEach(() => {
      // Setup default zone mocks
      mockPrisma.zone.findUnique
        .mockImplementation((args) => {
          if (args.where.id === pickupZoneId) {
            return Promise.resolve(mockPickupZone);
          }
          if (args.where.id === destinationZoneId) {
            return Promise.resolve(mockDestinationZone);
          }
          return Promise.resolve(null);
        });

      mockPrisma.zone.findMany.mockResolvedValue(mockStopsZones);
    });

    it('should return empty array when no city products provided', async () => {
      const result = await repository.findMaxPriorityFareForCityProducts(
        [],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toEqual([]);
    });

    it('should find Priority 1: Exact zone match', async () => {
      const mockFare = {
        id: 'fare1',
        cityProductId: 'cp1',
        priority: 1,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: pickupZoneId,
        toZoneId: destinationZoneId,
        fromZoneTypeId: null,
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      // Mock the repository's findOne method to return the exact match
      jest.spyOn(repository, 'findOne').mockResolvedValueOnce(mockFare);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
      expect(repository.findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            cityProductId: 'cp1',
            fromZoneId: pickupZoneId,
            toZoneId: destinationZoneId,
            priority: 1
          })
        })
      );
    });

    it('should find Priority 2: Stops override (zone)', async () => {
      const mockFare = {
        id: 'fare2',
        cityProductId: 'cp1',
        priority: 2,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: 'stop-zone-1',
        fromZoneTypeId: null,
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      // Mock no exact match found, but stops override found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValueOnce(null) // Priority 1 not found
        .mockResolvedValueOnce(mockFare); // Priority 2 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
    });

    it('should find Priority 3: Stops override (zone type)', async () => {
      const mockFare = {
        id: 'fare3',
        cityProductId: 'cp1',
        priority: 3,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: null,
        fromZoneTypeId: null,
        toZoneTypeId: 'stop-zone-type-1',
        fareChargeGroups: []
      };

      // Mock no exact match or zone stops override, but zone type stops override found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValueOnce(null) // Priority 1 not found
        .mockResolvedValueOnce(null) // Priority 2 not found
        .mockResolvedValueOnce(mockFare); // Priority 3 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
    });

    it('should find Priority 4: Exact destination zone', async () => {
      const mockFare = {
        id: 'fare4',
        cityProductId: 'cp1',
        priority: 4,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: destinationZoneId,
        fromZoneTypeId: null,
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      // Mock higher priorities not found, destination zone match found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValue(null) // Priorities 1-3 not found
        .mockResolvedValueOnce(mockFare); // Priority 4 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
    });

    it('should find Priority 5: Exact pickup zone', async () => {
      const mockFare = {
        id: 'fare5',
        cityProductId: 'cp1',
        priority: 5,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: pickupZoneId,
        toZoneId: null,
        fromZoneTypeId: null,
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      // Mock higher priorities not found, pickup zone match found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValue(null) // Priorities 1-4 not found
        .mockResolvedValueOnce(mockFare); // Priority 5 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
    });

    it('should find Priority 6: ZoneType to ZoneType match', async () => {
      const mockFare = {
        id: 'fare6',
        cityProductId: 'cp1',
        priority: 6,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: null,
        fromZoneTypeId: 'pickup-zone-type-1',
        toZoneTypeId: 'dest-zone-type-1',
        fareChargeGroups: []
      };

      // Mock higher priorities not found, zone type to zone type match found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValue(null) // Priorities 1-5 not found
        .mockResolvedValueOnce(mockFare); // Priority 6 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
    });

    it('should find Priority 7: Destination type-only', async () => {
      const mockFare = {
        id: 'fare7',
        cityProductId: 'cp1',
        priority: 7,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: null,
        fromZoneTypeId: null,
        toZoneTypeId: 'dest-zone-type-1',
        fareChargeGroups: []
      };

      // Mock higher priorities not found, destination type only found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValue(null) // Priorities 1-6 not found
        .mockResolvedValueOnce(mockFare); // Priority 7 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
    });

    it('should find Priority 8: Pickup type-only', async () => {
      const mockFare = {
        id: 'fare8',
        cityProductId: 'cp1',
        priority: 8,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: null,
        fromZoneTypeId: 'pickup-zone-type-1',
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      // Mock higher priorities not found, pickup type only found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValue(null) // Priorities 1-7 not found
        .mockResolvedValueOnce(mockFare); // Priority 8 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFare);
    });

    it('should find Priority 9: Fallback when no other matches', async () => {
      const mockFallbackFare = {
        id: 'fare9',
        cityProductId: 'cp1',
        priority: 9,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: null,
        fromZoneTypeId: null,
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      // Mock all higher priorities returning null, only fallback found
      jest.spyOn(repository, 'findOne')
        .mockResolvedValue(null) // All priorities 1-8 not found
        .mockResolvedValueOnce(mockFallbackFare); // Priority 9 found

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFallbackFare);
    });

    it('should handle multiple city products and return best fare for each', async () => {
      const mockFare1 = {
        id: 'fare1',
        cityProductId: 'cp1',
        priority: 1,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: pickupZoneId,
        toZoneId: destinationZoneId,
        fareChargeGroups: []
      };

      const mockFare2 = {
        id: 'fare2',
        cityProductId: 'cp2',
        priority: 4,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: destinationZoneId,
        fareChargeGroups: []
      };

      // Mock different priority matches for different city products
      jest.spyOn(repository, 'findOne')
        .mockResolvedValueOnce(mockFare1) // cp1 finds exact match (priority 1)
        .mockResolvedValueOnce(null) // cp2 priority 1 not found
        .mockResolvedValueOnce(null) // cp2 priority 2 not found
        .mockResolvedValueOnce(null) // cp2 priority 3 not found
        .mockResolvedValueOnce(mockFare2); // cp2 finds destination match (priority 4)

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1', 'cp2'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual(mockFare1);
      expect(result[1]).toEqual(mockFare2);
    });

    it('should return empty array when no fares found for any city product', async () => {
      // Mock all findOne calls returning null
      jest.spyOn(repository, 'findOne').mockResolvedValue(null);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        pickupZoneId,
        destinationZoneId,
        stopsZoneIds
      );

      expect(result).toEqual([]);
    });

    it('should handle null zones gracefully', async () => {
      const mockFallbackFare = {
        id: 'fare9',
        cityProductId: 'cp1',
        priority: 9,
        status: CityProductFareStatus.ACTIVE,
        fromZoneId: null,
        toZoneId: null,
        fromZoneTypeId: null,
        toZoneTypeId: null,
        fareChargeGroups: []
      };

      jest.spyOn(repository, 'findOne').mockResolvedValue(mockFallbackFare);

      const result = await repository.findMaxPriorityFareForCityProducts(
        ['cp1'],
        null,
        null,
        null
      );

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockFallbackFare);
    });
  });
});
