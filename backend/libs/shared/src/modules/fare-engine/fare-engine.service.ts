import { Injectable, Logger } from "@nestjs/common";
import { FareCalculationResult, FareContext } from "./interfaces/interface";
import { CityProductFareRepository } from "../../repositories/city-product-fare.repository";

@Injectable()
export class FareEngineService {
    private readonly logger = new Logger(FareEngineService.name);

    constructor(
        private readonly cityProductFareRepository: CityProductFareRepository,
    ) { }


    /**
     *  Estimate fare for a ride
     */
    async estimateFare(request: FareContext): Promise<FareCalculationResult> {
        const { pickup, destination, stops, cityProducts } = request;

        try {
            // Extract zone IDs
            const pickupZoneId = pickup?.id || null;
            const destinationZoneId = destination?.id || null;
            const stopsZoneIds = stops?.map(stop => stop.id) || null;

            // Extract city product IDs
            const cityProductIds = cityProducts.map(cp => cp.id);

            if (cityProductIds.length === 0) {
                this.logger.warn('No city products provided for fare estimation');
                return {
                    status: false,
                    data: {
                        error: 'No city products available for fare estimation'
                    }
                };
            }

            // Get maximum priority fares for each city product
            const maxPriorityFares = await this.cityProductFareRepository.findMaxPriorityFareForCityProducts(
                cityProductIds,
                pickupZoneId,
                destinationZoneId,
                stopsZoneIds
            );

            if (maxPriorityFares.length === 0) {
                this.logger.warn('No matching fare rules found for the given zones and city products');
                return {
                    status: false,
                    data: {
                        error: 'No fare rules found for the specified route'
                    }
                };
            }

            // Log the selected fares for debugging
            this.logger.debug(`Found ${maxPriorityFares.length} fare rules for estimation`, {
                pickupZoneId,
                destinationZoneId,
                stopsZoneIds,
                fareRules: maxPriorityFares.map(fare => ({
                    cityProductId: fare.cityProductId,
                    priority: fare.priority,
                    fromZoneId: fare.fromZoneId,
                    toZoneId: fare.toZoneId,
                    chargeGroupsCount: fare.fareChargeGroups?.length || 0
                }))
            });

            return {
                status: true,
                data: {
                    fareRules: maxPriorityFares,
                    summary: {
                        totalRules: maxPriorityFares.length,
                        pickupZone: pickup?.name || 'Unknown',
                        destinationZone: destination?.name || 'Unknown',
                        stopsCount: stops?.length || 0
                    }
                }
            };

        } catch (error) {
            this.logger.error('Error estimating fare', error);
            return {
                status: false,
                data: {
                    error: 'Internal error during fare estimation'
                }
            };
        }
    }
}

