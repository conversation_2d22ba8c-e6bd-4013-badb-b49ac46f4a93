import { Injectable, Logger } from "@nestjs/common";
import { FareContext, FareRuleMatchResult } from "./interfaces/interface";
import { FareMatcherService } from "./fare-matcher.service";

@Injectable()
export class FareEngineService {
    private readonly logger = new Logger(FareEngineService.name);

    constructor(
        private readonly fareMatcherService: FareMatcherService,
    ) { }

    async estimateFare(request: FareContext): Promise<any> {
        const { pickup, destination, stops, cityProducts } = request;

        this.logger.debug('Starting fare estimation', {
            pickupZone: pickup?.name || 'None',
            destinationZone: destination?.name || 'None',
            stopsCount: stops?.length || 0,
            cityProductsCount: cityProducts.length
        });

        try {
            const matchingResult: FareRuleMatchResult = await this.fareMatcherService.findMatchingFares(
                pickup,
                destination,
                stops,
                cityProducts
            );

            if (!matchingResult.status) {
                this.logger.warn('Fare matching failed', matchingResult.data);
                //TODO: handle the error here
                return false;
            }

            // Type guard to ensure we have FareCalculationData
            if (matchingResult.data && 'fareRules' in matchingResult.data) {
                this.logger.debug('Fare rule matching completed successfully', {
                    totalRules: matchingResult.data.summary.totalRules
                });
            }

            console.log({ matchingResult })

            //now we have cityProductFare for each cityproduct 
            if(matchingResult.data?.fareRules)
            return true;

        } catch (error) {
            this.logger.error('Error during fare estimation orchestration', error);
            return {
                status: false,
                data: {
                    error: 'Internal error during fare estimation'
                }
            };
        }
    }
}

