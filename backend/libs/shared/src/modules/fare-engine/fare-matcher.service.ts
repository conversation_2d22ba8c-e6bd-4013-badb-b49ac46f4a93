import { Injectable, Logger } from "@nestjs/common";
import { CityProductFareRepository } from "../../repositories/city-product-fare.repository";
import {
    FareRuleMatchResult,
    FareCalculationData,
    FareCalculationError,
    FareMatchingParams
} from "./interfaces/interface";
import { Zone } from "@shared/shared/repositories/models/zone.model";
import { CityProduct } from "@shared/shared/repositories/models/cityProduct.model";

@Injectable()
export class FareMatcherService {
    private readonly logger = new Logger(FareMatcherService.name);

    constructor(
        private readonly cityProductFareRepository: CityProductFareRepository,
    ) { }

    /**
     * Find the best matching fares for given pickup, destination, stops and city products
     * using the 9-tier priority ranking system.
     * 
     * @param pickup - Pickup zone (nullable)
     * @param destination - Destination zone (nullable)
     * @param stops - Array of stop zones (nullable)
     * @param cityProducts - Array of city products to find fares for
     * @returns Promise<FareRuleMatchResult> - Result containing matched fares or error
     */
    async findMatchingFares(
        pickup: Zone | null,
        destination: Zone | null,
        stops: Zone[] | null,
        cityProducts: CityProduct[]
    ): Promise<FareRuleMatchResult> {
        try {
            // Extract zone IDs
            const pickupZoneId = pickup?.id || null;
            const destinationZoneId = destination?.id || null;
            const stopsZoneIds = stops?.map(stop => stop.id) || null;

            // Extract city product IDs
            const cityProductIds = cityProducts.map(cp => cp.id);

            if (cityProductIds.length === 0) {
                this.logger.warn('No city products provided for fare matching');
                return {
                    status: false,
                    data: {
                        error: 'No city products available for fare matching'
                    } as FareCalculationError
                };
            }

            // Get maximum priority fares for each city product using the new priority-based matching
            const maxPriorityFares = await this.cityProductFareRepository.findMaxPriorityFareForCityProducts(
                cityProductIds,
                pickupZoneId,
                destinationZoneId,
                stopsZoneIds
            );

            if (maxPriorityFares.length === 0) {
                this.logger.warn('No matching fare rules found for the given zones and city products');
                return {
                    status: false,
                    data: {
                        error: 'No fare rules found for the specified route'
                    } as FareCalculationError
                };
            }

            // Log the selected fares for debugging
            this.logger.debug(`Found ${maxPriorityFares.length} fare rules for matching`, {
                pickupZoneId,
                destinationZoneId,
                stopsZoneIds,
                fareRules: maxPriorityFares.map(fare => ({
                    cityProductId: fare.cityProductId,
                    priority: fare.priority,
                    fromZoneId: fare.fromZoneId,
                    toZoneId: fare.toZoneId,
                    fromZoneTypeId: fare.fromZoneTypeId,
                    toZoneTypeId: fare.toZoneTypeId,
                    chargeGroupsCount: fare.fareChargeGroups?.length || 0
                }))
            });

            return {
                status: true,
                data: {
                    fareRules: maxPriorityFares,
                    summary: {
                        totalRules: maxPriorityFares.length,
                        pickupZone: pickup?.name || 'Unknown',
                        destinationZone: destination?.name || 'Unknown',
                        stopsCount: stops?.length || 0
                    }
                } as FareCalculationData
            };

        } catch (error) {
            this.logger.error('Error during fare matching', error);
            return {
                status: false,
                data: {
                    error: 'Internal error during fare matching'
                } as FareCalculationError
            };
        }
    }

    /**
     * Find matching fares using parameters object
     * @param params - Fare matching parameters
     * @returns Promise<FareRuleMatchResult> - Result containing matched fares or error
     */
    async findMatchingFaresByParams(params: FareMatchingParams): Promise<FareRuleMatchResult> {
        try {
            const { cityProductIds, pickupZoneId, destinationZoneId, stopsZoneIds } = params;

            if (cityProductIds.length === 0) {
                this.logger.warn('No city products provided for fare matching');
                return {
                    status: false,
                    data: {
                        error: 'No city products available for fare matching'
                    } as FareCalculationError
                };
            }

            // Get maximum priority fares for each city product
            const maxPriorityFares = await this.cityProductFareRepository.findMaxPriorityFareForCityProducts(
                cityProductIds,
                pickupZoneId,
                destinationZoneId,
                stopsZoneIds
            );

            if (maxPriorityFares.length === 0) {
                this.logger.warn('No matching fare rules found for the given parameters');
                return {
                    status: false,
                    data: {
                        error: 'No fare rules found for the specified parameters'
                    } as FareCalculationError
                };
            }

            // Log the selected fares for debugging
            this.logger.debug(`Found ${maxPriorityFares.length} fare rules for parameters`, {
                params,
                fareRules: maxPriorityFares.map(fare => ({
                    cityProductId: fare.cityProductId,
                    priority: fare.priority,
                    fromZoneId: fare.fromZoneId,
                    toZoneId: fare.toZoneId,
                    fromZoneTypeId: fare.fromZoneTypeId,
                    toZoneTypeId: fare.toZoneTypeId,
                    chargeGroupsCount: fare.fareChargeGroups?.length || 0
                }))
            });

            return {
                status: true,
                data: {
                    fareRules: maxPriorityFares,
                    summary: {
                        totalRules: maxPriorityFares.length,
                        pickupZone: 'N/A',
                        destinationZone: 'N/A',
                        stopsCount: stopsZoneIds?.length || 0
                    }
                } as FareCalculationData
            };

        } catch (error) {
            this.logger.error('Error during fare matching by parameters', error);
            return {
                status: false,
                data: {
                    error: 'Internal error during fare matching'
                } as FareCalculationError
            };
        }
    }

    /**
     * Get the best fare for a specific city product
     * @param cityProductId - City product ID
     * @param pickup - Pickup zone (nullable)
     * @param destination - Destination zone (nullable)
     * @param stops - Array of stop zones (nullable)
     * @returns Promise<CityProductFare | null> - Best matching fare or null if none found
     */
    async findBestFareForCityProduct(
        cityProductId: string,
        pickup: Zone | null,
        destination: Zone | null,
        stops: Zone[] | null
    ) {
        const result = await this.findMatchingFares(pickup, destination, stops, [{ id: cityProductId } as CityProduct]);

        if (result.status && result.data && 'fareRules' in result.data) {
            return result.data.fareRules[0] || null;
        }

        return null;
    }
}
