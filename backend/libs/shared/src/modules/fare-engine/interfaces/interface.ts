import { CityProduct } from "@shared/shared/repositories/models/cityProduct.model";
import { CityProductFare } from "@shared/shared/repositories/models/cityProductFare.model";
import { RideBookingType } from "../../ride/ride-search.service";
import { Zone } from "@shared/shared/repositories/models/zone.model";

export interface FareContext {
    pickup: Zone | null;
    destination: Zone | null;
    stops: Zone[] | null;
    cityProducts: CityProduct[];
    type?: RideBookingType | undefined;
    pickupTime?: string | undefined;
}
export interface FareCalculationData {
    fareRules: CityProductFare[];
    summary: FareCalculationSummary;
}

export interface FareCalculationError {
    error: string;
}

export interface FareCalculationSummary {
    totalRules: number;
    pickupZone: string;
    destinationZone: string;
    stopsCount: number;
}

export interface FareRuleMatchResult {
    status: boolean;
    data: FareCalculationData | FareCalculationError | undefined;
}


/**
 * Parameters for fare matching operations
 */
export interface FareMatchingParams {
    cityProductIds: string[];
    pickupZoneId?: string | null;
    destinationZoneId?: string | null;
    stopsZoneIds?: string[] | null;
}

/**
 * Represents the priority ranking for fare matching
 */
export enum FareMatchingPriority {
    EXACT_ZONE_MATCH = 1,
    STOPS_ZONE_OVERRIDE = 2,
    STOPS_ZONE_TYPE_OVERRIDE = 3,
    EXACT_DESTINATION_ZONE = 4,
    EXACT_PICKUP_ZONE = 5,
    ZONE_TYPE_TO_ZONE_TYPE = 6,
    DESTINATION_TYPE_ONLY = 7,
    PICKUP_TYPE_ONLY = 8,
    FALLBACK_DEFAULT = 9
}

/**
 * Detailed information about how a fare was matched
 */
export interface FareMatchingResult {
    fare: CityProductFare;
    matchedPriority: FareMatchingPriority;
    matchingReason: string;
}