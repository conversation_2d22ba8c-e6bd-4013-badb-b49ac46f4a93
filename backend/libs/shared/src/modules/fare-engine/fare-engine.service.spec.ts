import { Test, TestingModule } from '@nestjs/testing';
import { FareEngineService } from './fare-engine.service';
import { CityProductFareRepository } from '../../repositories/city-product-fare.repository';
import { FareContext } from './interfaces/interface';
import { CityProductFareStatus } from '../../repositories/models/cityProductFare.model';

describe('FareEngineService', () => {
  let service: FareEngineService;
  let mockRepository: jest.Mocked<CityProductFareRepository>;

  beforeEach(async () => {
    const mockRepo = {
      findMaxPriorityFareForCityProducts: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FareEngineService,
        {
          provide: CityProductFareRepository,
          useValue: mockRepo,
        },
      ],
    }).compile();

    service = module.get<FareEngineService>(FareEngineService);
    mockRepository = module.get(CityProductFareRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('estimateFare', () => {
    const mockCityProducts = [
      { id: 'cp1', name: 'Standard Taxi' },
      { id: 'cp2', name: 'Premium Taxi' },
    ];

    const mockPickupZone = { id: 'zone1', name: 'Airport' };
    const mockDestinationZone = { id: 'zone2', name: 'Downtown' };
    const mockStops = [{ id: 'zone3', name: 'Mall' }];

    it('should return fare rules when matching fares are found', async () => {
      const mockFareRules = [
        {
          id: 'fare1',
          cityProductId: 'cp1',
          priority: 10,
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: 'zone1',
          toZoneId: 'zone2',
          fareChargeGroups: [],
        },
        {
          id: 'fare2',
          cityProductId: 'cp2',
          priority: 5,
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: 'zone1',
          toZoneId: 'zone2',
          fareChargeGroups: [],
        },
      ];

      mockRepository.findMaxPriorityFareForCityProducts.mockResolvedValue(mockFareRules);

      const request: FareContext = {
        pickup: mockPickupZone,
        destination: mockDestinationZone,
        stops: mockStops,
        cityProducts: mockCityProducts,
      };

      const result = await service.estimateFare(request);

      expect(result.status).toBe(true);
      expect(result.data.fareRules).toEqual(mockFareRules);
      expect(result.data.summary.totalRules).toBe(2);
      expect(mockRepository.findMaxPriorityFareForCityProducts).toHaveBeenCalledWith(
        ['cp1', 'cp2'],
        'zone1',
        'zone2',
        ['zone3']
      );
    });

    it('should handle case when no city products are provided', async () => {
      const request: FareContext = {
        pickup: mockPickupZone,
        destination: mockDestinationZone,
        stops: null,
        cityProducts: [],
      };

      const result = await service.estimateFare(request);

      expect(result.status).toBe(false);
      expect(result.data.error).toBe('No city products available for fare estimation');
    });

    it('should handle case when no fare rules are found', async () => {
      mockRepository.findMaxPriorityFareForCityProducts.mockResolvedValue([]);

      const request: FareContext = {
        pickup: mockPickupZone,
        destination: mockDestinationZone,
        stops: null,
        cityProducts: mockCityProducts,
      };

      const result = await service.estimateFare(request);

      expect(result.status).toBe(false);
      expect(result.data.error).toBe('No fare rules found for the specified route');
    });

    it('should handle null pickup and destination zones', async () => {
      const mockFareRules = [
        {
          id: 'fare1',
          cityProductId: 'cp1',
          priority: 1,
          status: CityProductFareStatus.ACTIVE,
          fromZoneId: null,
          toZoneId: null,
          fareChargeGroups: [],
        },
      ];

      mockRepository.findMaxPriorityFareForCityProducts.mockResolvedValue(mockFareRules);

      const request: FareContext = {
        pickup: null,
        destination: null,
        stops: null,
        cityProducts: mockCityProducts,
      };

      const result = await service.estimateFare(request);

      expect(result.status).toBe(true);
      expect(mockRepository.findMaxPriorityFareForCityProducts).toHaveBeenCalledWith(
        ['cp1', 'cp2'],
        null,
        null,
        null
      );
    });

    it('should handle repository errors gracefully', async () => {
      mockRepository.findMaxPriorityFareForCityProducts.mockRejectedValue(
        new Error('Database error')
      );

      const request: FareContext = {
        pickup: mockPickupZone,
        destination: mockDestinationZone,
        stops: null,
        cityProducts: mockCityProducts,
      };

      const result = await service.estimateFare(request);

      expect(result.status).toBe(false);
      expect(result.data.error).toBe('Internal error during fare estimation');
    });
  });
});
